# Beezer 数据监控测试配置文件
# 使用 Mock Inbound 和 Print Outbound 进行测试

server_port: 8000
debug: true

# 流配置
flows:
  # 传感器数据流
  - name: "sensor_data_flow"
    inbounds:
      - id: "mock_sensor_001"
        type: "mock"
        interval: 3
        data_type: "sensor"
        error_rate: 0.05
      
      - id: "mock_sensor_002"
        type: "mock"
        interval: 5
        data_type: "sensor"
        error_rate: 0.02
    
    rules:
      - id: "sensor_mapping"
        type: "DataMapping"
        trigger:
          mode: "interval"
        mapping:
          # 简单的数据映射
          device_id: "$.id"
          timestamp: "$.timestamp"
          temperature: "$.sensors.temperature"
          humidity: "$.sensors.humidity"
          location: "$.location"
        action: "print"
    
    outbound:
      id: "print_sensor"
      type: "print"
      format: "json"
      prefix: "[SENSOR]"
      max_length: 300

  # 机器数据流
  - name: "machine_data_flow"
    inbounds:
      - id: "mock_machine_001"
        type: "mock"
        interval: 2
        data_type: "machine"
        error_rate: 0.1
      
      - id: "mock_machine_002"
        type: "mock"
        interval: 4
        data_type: "machine"
        error_rate: 0.03
    
    rules:
      - id: "machine_mapping"
        type: "DataMapping"
        trigger:
          mode: "interval"
        mapping:
          # 机器数据映射
          machine_id: "$.id"
          timestamp: "$.timestamp"
          speed: "$.machine.speed"
          load: "$.machine.load"
          power: "$.machine.power"
          production_count: "$.production.count"
          quality: "$.production.quality"
        action: "log"
    
    outbound:
      id: "print_machine"
      type: "print"
      format: "detailed"
      prefix: "[MACHINE]"
      max_length: 500

  # Modbus模拟数据流
  - name: "modbus_simulation_flow"
    inbounds:
      - id: "mock_modbus_001"
        type: "mock"
        interval: 1
        data_type: "modbus"
        error_rate: 0.02
    
    rules:
      - id: "modbus_mapping"
        type: "DataMapping"
        trigger:
          mode: "interval"
        mapping:
          # Modbus数据映射
          device_id: "$.id"
          timestamp: "$.timestamp"
          registers: "$.registers"
          coils: "$.coils"
          function_code: "$.function_code"
        action: "debug"
    
    outbound:
      id: "print_modbus"
      type: "print"
      format: "simple"
      prefix: "[MODBUS]"
      max_length: 200

  # 通用数据流（用于测试错误处理）
  - name: "generic_data_flow"
    inbounds:
      - id: "mock_generic_001"
        type: "mock"
        interval: 6
        data_type: "generic"
        error_rate: 0.2  # 较高的错误率用于测试错误处理
    
    rules:
      - id: "generic_mapping"
        type: "DataMapping"
        trigger:
          mode: "interval"
        mapping:
          # 通用数据映射
          source_id: "$.id"
          timestamp: "$.timestamp"
          values: "$.values"
          flags: "$.flags"
          message: "$.message"
        action: "print"
    
    outbound:
      id: "print_generic"
      type: "print"
      format: "json"
      prefix: "[GENERIC]"
      max_length: 400

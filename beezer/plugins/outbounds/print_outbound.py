"""Print Outbound 插件，用于测试数据监控功能。

这个插件会将数据打印到控制台，用于测试监控系统。
"""

import json
import time
from typing import Dict, Any
from loguru import logger

from beezer.plugins._base import OutboundPlugin


class PrintOutboundConfig:
    """Print Outbound 配置。"""
    
    def __init__(self, config: Dict[str, Any]):
        self.id = config.get("id", "print_001")
        self.format = config.get("format", "json")  # json, simple, detailed
        self.prefix = config.get("prefix", "[PRINT]")
        self.max_length = config.get("max_length", 500)  # 最大打印长度


class PrintOutbound(OutboundPlugin):
    """Print 数据输出插件。"""
    
    plugin_protocol = "print"
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.config = PrintOutboundConfig(config)
        self._output_counter = 0
        
        # 注册动作
        self.register_action("print", self.action_print)
        self.register_action("log", self.action_log)
        self.register_action("debug", self.action_debug)
        
        logger.info(f"Print Outbound {self.config.id} 已初始化")
    
    async def write(self, data: Any, config: Dict[str, Any]) -> Any:
        """默认写入实现。"""
        return await self.action_print(data, config)
    
    async def action_print(self, data: Any, config: Dict[str, Any]) -> Any:
        """打印动作。"""
        try:
            await self.report_status("running", {
                "action": "print",
                "output_count": self._output_counter,
                "last_output_time": time.strftime("%H:%M:%S"),
            })
            
            formatted_data = self._format_data(data)
            print(f"{self.config.prefix} {formatted_data}")
            
            self._output_counter += 1
            
            # 定期上报状态
            if self._output_counter % 5 == 0:
                await self.report_status("running", {
                    "total_outputs": self._output_counter,
                    "format": self.config.format,
                    "last_action": "print",
                })
            
            return {"success": True, "output_count": self._output_counter}
            
        except Exception as e:
            await self.report_status("error", {
                "error_message": str(e),
                "action": "print",
                "failed_data_type": type(data).__name__,
            })
            logger.error(f"Print输出失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def action_log(self, data: Any, config: Dict[str, Any]) -> Any:
        """日志动作。"""
        try:
            await self.report_status("running", {
                "action": "log",
                "output_count": self._output_counter,
            })
            
            formatted_data = self._format_data(data)
            logger.info(f"[LOG OUTPUT] {formatted_data}")
            
            self._output_counter += 1
            
            return {"success": True, "output_count": self._output_counter}
            
        except Exception as e:
            await self.report_status("error", {
                "error_message": str(e),
                "action": "log",
            })
            logger.error(f"Log输出失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def action_debug(self, data: Any, config: Dict[str, Any]) -> Any:
        """调试动作。"""
        try:
            await self.report_status("running", {
                "action": "debug",
                "output_count": self._output_counter,
            })
            
            # 详细的调试信息
            debug_info = {
                "timestamp": time.time(),
                "data_type": type(data).__name__,
                "data_size": len(str(data)),
                "config": config,
                "data": data,
            }
            
            formatted_debug = json.dumps(debug_info, ensure_ascii=False, indent=2)
            print(f"[DEBUG] {formatted_debug}")
            
            self._output_counter += 1
            
            return {"success": True, "output_count": self._output_counter, "debug_info": debug_info}
            
        except Exception as e:
            await self.report_status("error", {
                "error_message": str(e),
                "action": "debug",
            })
            logger.error(f"Debug输出失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _format_data(self, data: Any) -> str:
        """格式化数据。"""
        try:
            if self.config.format == "json":
                formatted = json.dumps(data, ensure_ascii=False, indent=None)
            elif self.config.format == "simple":
                if isinstance(data, dict):
                    # 提取关键字段
                    key_fields = ["id", "timestamp", "status", "value", "message"]
                    simple_data = {k: v for k, v in data.items() if k in key_fields}
                    formatted = json.dumps(simple_data, ensure_ascii=False)
                else:
                    formatted = str(data)
            elif self.config.format == "detailed":
                formatted = json.dumps(data, ensure_ascii=False, indent=2)
            else:
                formatted = str(data)
            
            # 限制长度
            if len(formatted) > self.config.max_length:
                formatted = formatted[:self.config.max_length] + "..."
            
            return formatted
            
        except Exception as e:
            logger.error(f"数据格式化失败: {e}")
            return f"<格式化失败: {str(data)[:100]}...>"

import os
import importlib
from loguru import logger
from typing import Dict, Any, Set
from beezer.type_model import (
    ConfigModel,
    InboundTypes,
    OutBoundModel,
    InboundConfig,
    RuleModel,
    RuleTypes,
)

# 显式导入所有插件模块
from beezer.plugins.inbounds import http_client as inbound_http_client
from beezer.plugins.inbounds import modbus_slave
from beezer.plugins.inbounds import fanuc as fanuc_inbound
from beezer.plugins.inbounds import siemens as SiemensInbound
from beezer.plugins.inbounds import mock_inbound
from beezer.plugins.outbounds import http_client as outbound_http_client
from beezer.plugins.outbounds import print_outbound
from beezer.plugins.rules import data_mapping
from beezer.plugins.rules import data_aggregation
from beezer.plugins.flows import _basic as basic_flow


# 插件类型到模块的映射
PLUGIN_MODULES = {
    InboundTypes.Modbus: modbus_slave,
    InboundTypes.HttpClient: inbound_http_client,
    InboundTypes.Fanuc: fanuc_inbound,
    InboundTypes.Siemens: SiemensInbound,
    "mock": mock_inbound,
    "xinheyun": outbound_http_client,
    "custom_ioteq": outbound_http_client,
    "print": print_outbound,
    RuleTypes.DataMapping: data_mapping,
    RuleTypes.DataAggregation: data_aggregation,
    "basic_flow": basic_flow,
}


def get_required_plugins(config: ConfigModel) -> Set[str]:
    """从配置中获取所需的插件类型

    Args:
        config: 应用配置对象

    Returns:
        需要加载的插件类型集合
    """
    required_plugins = set()

    # 收集 inbound 类型
    for inbound_config in config.inbounds.values():
        required_plugins.add(inbound_config.type)

    # 收集 outbound 类型
    for outbound_config in config.outbounds.values():
        required_plugins.add(outbound_config.type)

    # 收集 rules 类型
    for rule_config in config.rules:
        # 假设规则类型都是 data_mapping
        required_plugins.add(RuleTypes.DataMapping.value)

    return required_plugins


def import_plugin_module(plugin_type: str) -> bool:
    """获取指定类型的插件模块

    Args:
        plugin_type: 插件类型名称

    Returns:
        是否成功获取
    """
    if plugin_type not in PLUGIN_MODULES:
        logger.warning(f"Unknown plugin type: {plugin_type}")
        return False

    logger.info(f"Plugin loaded: {plugin_type}")
    return True


def load_plugins(config: ConfigModel):
    """验证配置中需要的插件是否都已加载

    Args:
        config: 应用配置对象
    """
    required_plugins = get_required_plugins(config)
    logger.info(f"Required plugins: {required_plugins}")

    loaded_count = 0
    for plugin_type in required_plugins:
        if import_plugin_module(plugin_type):
            loaded_count += 1

    logger.info(f"Successfully loaded {loaded_count} plugins")

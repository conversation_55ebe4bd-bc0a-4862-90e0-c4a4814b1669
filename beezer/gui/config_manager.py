"""Beezer GUI 配置管理模块。

负责加载、解析、验证和保存 Beezer 配置文件。
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
from loguru import logger
from pydantic import ValidationError

from beezer.type_model import (
    ConfigModel,
    InboundConfig,
    OutBoundModel,
    RuleModel,
    FlowModel,
    InboundTypes,
)


class ConfigManager:
    """配置管理器，处理 Beezer 配置的加载、保存和验证。"""

    def __init__(self, config_path: Optional[Path] = None):
        """初始化配置管理器。

        Args:
            config_path: 配置文件路径，默认为 config.yaml
        """
        self.config_path = config_path or Path("config.yaml")
        self.config: Optional[ConfigModel] = None
        self._config_data: Dict[str, Any] = {}

    async def load_config(self) -> bool:
        """加载配置文件。

        Returns:
            bool: 加载是否成功
        """
        try:
            logger.info(f"开始加载配置文件: {self.config_path}")

            if not self.config_path.exists():
                logger.warning(f"配置文件不存在: {self.config_path}")
                # 创建默认配置
                await self.create_default_config()
                return True

            logger.info("读取 YAML 文件...")
            with open(self.config_path, "r", encoding="utf-8") as f:
                self._config_data = yaml.safe_load(f) or {}

            logger.info(f"YAML 解析成功，顶级键: {list(self._config_data.keys())}")

            # 检查必需字段
            required_fields = ["apps", "inbounds", "outbounds", "rules", "flows"]
            missing_fields = []
            for field in required_fields:
                if field not in self._config_data:
                    missing_fields.append(field)
                    self._config_data[field] = (
                        [] if field in ["apps", "rules", "flows"] else {}
                    )

            if missing_fields:
                logger.warning(f"配置文件缺少字段，已自动添加默认值: {missing_fields}")

            # 添加默认的 server_port 和 version
            if "server_port" not in self._config_data:
                self._config_data["server_port"] = 9999
            if "version" not in self._config_data:
                self._config_data["version"] = 1

            logger.info("开始验证配置...")
            # 验证配置
            self.config = ConfigModel.model_validate(self._config_data)

            # 统计信息
            inbound_count = len(self.config.inbounds)
            outbound_count = len(self.config.outbounds)
            rule_count = len(self.config.rules)
            flow_count = len(self.config.flows)

            logger.info(
                f"配置文件加载成功: Inbound={inbound_count}, Outbound={outbound_count}, Rule={rule_count}, Flow={flow_count}"
            )
            return True

        except ValidationError as e:
            logger.error(f"配置文件验证失败: {e}")
            # 记录详细的验证错误
            for error in e.errors():
                logger.error(f"验证错误: {error['loc']} - {error['msg']}")
            return False
        except yaml.YAMLError as e:
            logger.error(f"YAML 解析失败: {e}")
            return False
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            import traceback

            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    async def save_config(self) -> bool:
        """保存配置到文件。

        Returns:
            bool: 保存是否成功
        """
        try:
            if not self.config:
                logger.error("没有配置数据可保存")
                return False

            # 将配置转换为字典，并处理特殊类型
            config_dict = self._serialize_config_for_yaml(self.config.model_dump())

            # 备份原配置文件
            if self.config_path.exists():
                backup_path = self.config_path.with_suffix(".yaml.bak")
                self.config_path.rename(backup_path)
                logger.info(f"原配置文件已备份到: {backup_path}")

            # 保存新配置
            with open(self.config_path, "w", encoding="utf-8") as f:
                yaml.dump(
                    config_dict,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    sort_keys=False,
                )

            logger.info(f"配置文件保存成功: {self.config_path}")
            return True

        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False

    def _serialize_config_for_yaml(self, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """将配置字典序列化为适合 YAML 的格式。

        Args:
            config_dict: 原始配置字典

        Returns:
            Dict[str, Any]: 序列化后的配置字典
        """

        def serialize_value(value):
            """递归序列化值。"""
            if isinstance(value, dict):
                return {k: serialize_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [serialize_value(item) for item in value]
            elif hasattr(value, "__class__") and hasattr(value.__class__, "__module__"):
                # 处理枚举类型
                if hasattr(value, "value"):
                    return value.value
                # 处理 IP 地址类型
                elif hasattr(value, "__str__"):
                    return str(value)
            return value

        return serialize_value(config_dict)

    async def create_default_config(self):
        """创建默认配置。"""
        self.config = ConfigModel(
            apps=[],
            inbounds={},
            outbounds={},
            rules=[],
            flows=[],
            server_port=9999,
            version=1,
        )
        self._config_data = self.config.model_dump()
        logger.info("创建了默认配置")

    def get_inbounds(self) -> Dict[str, InboundConfig]:
        """获取所有 Inbound 配置。

        Returns:
            Dict[str, InboundConfig]: Inbound 配置字典
        """
        if not self.config:
            return {}
        return {str(k): v for k, v in self.config.inbounds.items()}

    def get_outbounds(self) -> Dict[str, OutBoundModel]:
        """获取所有 Outbound 配置。

        Returns:
            Dict[str, OutBoundModel]: Outbound 配置字典
        """
        if not self.config:
            return {}
        return self.config.outbounds

    def get_rules(self) -> List[RuleModel]:
        """获取所有 Rule 配置。

        Returns:
            List[RuleModel]: Rule 配置列表
        """
        if not self.config:
            return []
        return self.config.rules

    def get_flows(self) -> List[FlowModel]:
        """获取所有 Flow 配置。

        Returns:
            List[FlowModel]: Flow 配置列表
        """
        if not self.config:
            return []
        return self.config.flows

    def add_inbound(self, inbound_id: str, inbound_config: InboundConfig) -> bool:
        """添加 Inbound 配置。

        Args:
            inbound_id: Inbound ID
            inbound_config: Inbound 配置

        Returns:
            bool: 添加是否成功
        """
        try:
            if not self.config:
                return False

            self.config.inbounds[inbound_id] = inbound_config
            logger.info(f"添加 Inbound: {inbound_id}")
            return True
        except Exception as e:
            logger.error(f"添加 Inbound 失败: {e}")
            return False

    def remove_inbound(self, inbound_id: str) -> bool:
        """删除 Inbound 配置。

        Args:
            inbound_id: Inbound ID

        Returns:
            bool: 删除是否成功
        """
        try:
            if not self.config or inbound_id not in self.config.inbounds:
                return False

            del self.config.inbounds[inbound_id]
            logger.info(f"删除 Inbound: {inbound_id}")
            return True
        except Exception as e:
            logger.error(f"删除 Inbound 失败: {e}")
            return False

    def add_outbound(self, outbound_id: str, outbound_config: OutBoundModel) -> bool:
        """添加 Outbound 配置。

        Args:
            outbound_id: Outbound ID
            outbound_config: Outbound 配置

        Returns:
            bool: 添加是否成功
        """
        try:
            if not self.config:
                return False

            self.config.outbounds[outbound_id] = outbound_config
            logger.info(f"添加 Outbound: {outbound_id}")
            return True
        except Exception as e:
            logger.error(f"添加 Outbound 失败: {e}")
            return False

    def remove_outbound(self, outbound_id: str) -> bool:
        """删除 Outbound 配置。

        Args:
            outbound_id: Outbound ID

        Returns:
            bool: 删除是否成功
        """
        try:
            if not self.config or outbound_id not in self.config.outbounds:
                return False

            del self.config.outbounds[outbound_id]
            logger.info(f"删除 Outbound: {outbound_id}")
            return True
        except Exception as e:
            logger.error(f"删除 Outbound 失败: {e}")
            return False

    def add_rule(self, rule: RuleModel) -> bool:
        """添加 Rule 配置。

        Args:
            rule: Rule 配置

        Returns:
            bool: 添加是否成功
        """
        try:
            if not self.config:
                return False

            self.config.rules.append(rule)
            logger.info(f"添加 Rule: {rule.name}")
            return True
        except Exception as e:
            logger.error(f"添加 Rule 失败: {e}")
            return False

    def remove_rule(self, rule_name: str) -> bool:
        """删除 Rule 配置。

        Args:
            rule_name: Rule 名称

        Returns:
            bool: 删除是否成功
        """
        try:
            if not self.config:
                return False

            self.config.rules = [r for r in self.config.rules if r.name != rule_name]
            logger.info(f"删除 Rule: {rule_name}")
            return True
        except Exception as e:
            logger.error(f"删除 Rule 失败: {e}")
            return False

    def validate_config(self) -> tuple[bool, List[str]]:
        """验证当前配置。

        Returns:
            tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []

        if not self.config:
            errors.append("配置未加载")
            return False, errors

        try:
            # 验证配置模型
            ConfigModel.model_validate(self.config.model_dump())
        except ValidationError as e:
            for error in e.errors():
                errors.append(f"{error['loc']}: {error['msg']}")

        return len(errors) == 0, errors

    def get_available_inbound_types(self) -> List[str]:
        """获取可用的 Inbound 类型。

        Returns:
            List[str]: Inbound 类型列表
        """
        return [t.value for t in InboundTypes]

    def get_available_outbound_types(self) -> List[str]:
        """获取可用的 Outbound 类型。

        Returns:
            List[str]: Outbound 类型列表
        """
        # 从插件模块中获取可用的 outbound 类型
        return ["xinheyun", "custom_ioteq", "websocket"]  # 可以扩展
